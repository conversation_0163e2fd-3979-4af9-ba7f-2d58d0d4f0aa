/* import { BezierEdge, BezierEdgeModel } from "@logicflow/core"; */
import { PolylineEdge, PolylineEdgeModel } from "@logicflow/core";

class FlowLinkModel extends PolylineEdgeModel {
  getEdgeStyle() {
    const style = super.getEdgeStyle();
    style.strokeWidth = 2;
    style.stroke = this.isSelected ? '#ff7f0e' : '#666666';
    return style;
  }
  /* setAttributes(data) {
      super.setAttributes(data)
      this.isHitable = true
	} */
}
class FlowLink extends PolylineEdge {}

export default {
  type: 'flow-link',
  view: FlowLink,
  model: FlowLinkModel
}

