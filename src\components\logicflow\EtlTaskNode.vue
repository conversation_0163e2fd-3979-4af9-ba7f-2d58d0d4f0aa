<template>
  <div class="task-node" :style="properties.style">
		<div>
			<div style="padding: 5px;background-color: #289ff9;">
				<img :src="getIconImage(properties.icon)"/>
				<span style="margin-left: 3px; color: #f4f4f4;font-weight: 300">{{properties.toolCategoryName}}</span>
			</div>
			<hr>
			<div style="margin-left: 8px;margin-top: 8px; font-weight: 500" v-if="properties.name.length <= 10">{{properties.name}}</div>
			<div style="margin-left: 8px;margin-top: 8px; font-weight: 500" v-else>{{properties.name.substring(0,10) + '...'}}</div>
		</div>
		<!-- <div style="margin-top:10px">
			<div><b>类型：{{properties.typeVal}}</b></div>
		</div> -->
  </div>
</template>

<script setup lang="ts">
	
const props = defineProps({
	//properties属性跟节点的properties绑定了
	properties: {
	  type: Object,
	  default: () => ({
			icon: '',
			name: '',
			toolType: '',
			toolCategoryId: '',
			toolCategoryName: '',
			nodeJson: '',
			weight: 1,
			overTimes: 10,
			failGoOn: 0,
			note: '',
			style: {}
	  })
	},
})

const getIconImage = (icon: any) => {
	 return `/images/etl/${icon}`; // 把图片移到public文件下
}
</script>
<style scoped>
.task-node {
  width: 180px;
  height: 88px;
	font-size: 15px;
	color: #737063;
	padding: 0;
	/* border-radius: 15px; */
	border: 2px solid #bababa;
	background-color: #fffefd;
  box-sizing: border-box;
}
</style>