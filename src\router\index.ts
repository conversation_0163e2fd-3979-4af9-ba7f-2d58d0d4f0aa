import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import store from '@/store'
import { i18n } from '@/i18n'
import { pathToCamel } from '@/utils/tool'
import logCollector from '../api/logCollector'

NProgress.configure({ showSpinner: false })

const constantRoutes: RouteRecordRaw[] = [
	{
		path: '/redirect',
		component: () => import('../layout/index.vue'),
		children: [
			{
				path: '/redirect/:path(.*)',
				component: () => import('../layout/components/Router/Redirect.vue')
			}
		]
	},
	{
		path: '/register',
		component: () => import('../views/register/index.vue')
	},
	{
		path: '/registerShare',
		component: () => import('../views/register/index-share.vue')
	},
	{
		path: '/login',
		component: () => import('../views/login/index.vue')
	},
	{
		path: '/404',
		component: () => import('../views/404.vue')
	},
	{
		path: '/front/statistics',
		component: () => import('../views/front/statistics/index.vue')
	},
	{
		path: '/front/statisticsDept',
		component: () => import('../views/front/statistics/dept.vue')
	},
	{
		path: '/loginShare',
		component: () => import('../views/login/index-share.vue')
	},
	// 问题描述详情
	{
		path: '/portal/workOrderDetail',
		name: 'WorkOrderDetail',
		component: () => import('@/views/portal/workOrderDetail.vue'),
		meta: {
			title: '问题描述详情'
		}
	},
	// 综合数据库管理路由
	{
		path: '/portal',
		name: 'portal',
		redirect: '/portal/index',
		component: () => import('@/views/portal/components/layout.vue'),
		children: [
			{
				path: 'news-list',
				name: 'NewsList',
				component: () => import('../views/news/news-list.vue'),
				meta: { title: '新闻列表', cache: true }
			},
			{
				path: 'news-detail', // 移除id占位符
				name: 'NewsDetail', // 必须保留name属性
				component: () => import('../views/news/news-detail.vue'),
				meta: { title: '新闻详情', cache: false }
			},

			{
				path: 'index',
				name: 'portalIndex',
				component: () => import('@/views/portal/index.vue'),
				meta: {
					title: '能力目录',
					titleDes: '根据资源分类、类型展示资源、提供了资源的检索、查看和申请服务'
				}
			},
			{
				path: 'detail',
				name: 'portalDetail',
				component: () => import('@/views/portal/detail.vue'),
				meta: {
					title: '接口详情',
					titleDes: '聚合数据，为多个平台实现数据共享'
				}
			},
			{
				path: 'contact',
				name: 'portalContact',
				component: () => import('@/views/portal/contact.vue'),
				meta: {
					title: '联系方式',
					titleDes: '联系方式描述文字'
				}
			},
			{
				path: 'problem',
				name: 'portalProblem',
				component: () => import('@/views/portal/problem.vue'),
				meta: {
					title: '问题描述',
					titleDes: '联系方式描述文字'
				}
			}
		]
	}
]

const asyncRoutes: RouteRecordRaw = {
	path: '/',
	component: () => import('../layout/index.vue'),
	redirect: '/home',
	children: [
		{
			path: '/home',
			name: 'Home',
			component: () => import('../views/home.vue'),
			meta: {
				title: i18n.global.t('router.home'),
				affix: true
			}
		},
		{
			path: '/profile/grzx',
			name: 'grzx',
			component: () => import('../views/profile/grzx.vue'),
			meta: {
				title: i18n.global.t('router.grzx'),
				cache: true
			}
		},
		{
			path: '/profile/password',
			name: 'ProfilePassword',
			component: () => import('../views/profile/password.vue'),
			meta: {
				title: i18n.global.t('router.profilePassword'),
				cache: true
			}
		},
		{
			path: '/data-assets/resource/db-resource',
			name: 'DataAssetsDbResource',
			component: () => import('../views/data-assets/resource/db-resource.vue'),
			meta: {
				title: '数据库表',
				cache: true
			}
		},
		{
			path: '/data-assets/resource/api-resource',
			name: 'DataAssetsApiResource',
			component: () => import('../views/data-assets/resource/api-resource.vue'),
			meta: {
				title: 'API',
				cache: false
			}
		},
		{
			path: '/data-assets/resource/file-resource',
			name: 'DataAssetsFileResource',
			component: () => import('../views/data-assets/resource/file-resource.vue'),
			meta: {
				title: '文件',
				cache: false
			}
		}
	]
}
// 配置常量菜单
export const constantMenu = [
	{
		id: 1000,
		name: 'Demo',
		url: null,
		openStyle: 0,
		icon: 'icon-windows',
		children: [
			{
				id: 1001,
				name: 'Icon 图标',
				url: 'demo/icons/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1002,
				name: '二维码生成',
				url: 'demo/qrcode/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1003,
				name: '页面打印',
				url: 'demo/printJs/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1004,
				name: '图片裁剪',
				url: 'demo/cropper/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1005,
				name: '富文本编辑器',
				url: 'demo/wangeditor/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			}
		]
	}
]

export const errorRoute: RouteRecordRaw = {
	path: '/:pathMatch(.*)',
	redirect: '/404'
}

export const router = createRouter({
	history: createWebHashHistory(),
	routes: constantRoutes
})

// 白名单列表
const whiteList = ['/register', '/registerShare', '/login', '/front/statistics', '/front/statisticsDept', '/portal/index', '/loginShare', '/portal/detail']

//综合数据库管理没有登录需要登录的页面需要跳转到loginShare
// const shareList = ['/portal/detail']

// 路由加载前
router.beforeEach(async (to, from, next) => {
	NProgress.start()
	// token存在的情况
	if (store.userStore.token) {
		logCollector.pageView(to, from)
		if (to.path === '/login') {
			next('/home')
		} else if (to.path === '/loginShare') {
			next('/portal/index')
		} else {
			// 用户信息不存在，则重新拉取用户等信息
			if (!store.userStore.user.id) {
				await store.userStore.getUserInfoAction()
				await store.userStore.getAuthorityListAction()
				await store.appStore.getDictListAction()
				await store.appStore.getProjectListAction()
				await store.appStore.getSysUserListAction()
				await store.appStore.getSysOrgListAction()
				const menuRoutes = await store.routerStore.getMenuRoutes()

				// 根据后端菜单路由，生成KeepAlive路由
				const keepAliveRoutes = getKeepAliveRoutes(menuRoutes, [])
				for (let i = 0; i < keepAliveRoutes.length; i++) {
					const item = keepAliveRoutes[i]
					if (item.meta?._blank) {
						// 外部打开菜单
						router.addRoute(item)
					} else {
						// 添加菜单
						asyncRoutes.children?.push(item)
					}
				}
				//console.log("keepAliveRoutes"+JSON.stringify(keepAliveRoutes))
				router.addRoute(asyncRoutes)

				// 错误路由
				router.addRoute(errorRoute)

				// 保存路由数据
				store.routerStore.setRoutes(constantRoutes.concat(asyncRoutes))

				next({ ...to, replace: true })
			} else {
				next()
			}
		}
	} else {
		// 没有token的情况下，可以进入白名单
		if (whiteList.indexOf(to.path) > -1) {
			next()
		} else {
			if (to.path === '/front/statistics') {
				next('/front/statistics')
			} else if (to.path === '/front/statisticsDept') {
				next('/front/statisticsDept')
			} else if (to.path === '/portal/index') {
				next('/portal/index')
			} else {
				next('/login')
				next('/register')
			}
		}
	}
})

// 路由加载后
router.afterEach(() => {
	NProgress.done()
})

// 获取扁平化路由，将多级路由转换成一级路由
export const getKeepAliveRoutes = (rs: RouteRecordRaw[], breadcrumb: string[]): RouteRecordRaw[] => {
	const routerList: RouteRecordRaw[] = []

	rs.forEach((item: any) => {
		if (item.meta.title) {
			breadcrumb.push(item.meta.title)
		}

		if (item.children && item.children.length > 0) {
			routerList.push(...getKeepAliveRoutes(item.children, breadcrumb))
		} else {
			item.meta.breadcrumb.push(...breadcrumb)
			routerList.push(item)
		}

		breadcrumb.pop()
	})
	return routerList
}

// 加载vue组件
const layoutModules = import.meta.glob('/src/views/**/*.vue')

// 根据路径，动态获取vue组件
const getDynamicComponent = (path: string): any => {
	const component = layoutModules[`/src/views/${path}.vue`]
	if (!component) {
		console.error('组件不存在，路径为：', path)
	}
	return component
}

// 根据菜单列表，生成路由数据
export const generateRoutes = (menuList: any): RouteRecordRaw[] => {
	const routerList: RouteRecordRaw[] = []

	menuList.forEach((menu: any) => {
		let component
		let path
		if (menu.children && menu.children.length > 0) {
			component = () => import('@/layout/index.vue')
			path = '/p/' + menu.id
		} else {
			component = getDynamicComponent(menu.url)
			path = '/' + menu.url
		}
		const route: RouteRecordRaw = {
			path: path,
			name: pathToCamel(path),
			component: component,
			children: [],
			meta: {
				title: menu.name,
				icon: menu.icon,
				id: '' + menu.id,
				cache: true,
				_blank: menu.openStyle === 1,
				breadcrumb: []
			}
		}

		// 有子菜单的情况
		if (menu.children && menu.children.length > 0) {
			route.children?.push(...generateRoutes(menu.children))
		}

		routerList.push(route)
	})

	return routerList
}
